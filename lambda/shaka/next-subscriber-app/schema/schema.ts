import { z } from 'zod';
import { isValidPortingDate } from '@/schemas/schemas';
import {
  SignupFormData,
  SignupPayload
} from '@/components/signup-form/signup-form';
import { transformToPayload } from '@/hooks/useSignUpFormSubmission';

const PAC_REGEX = /^pac\d{6}$/i;
// const PAC_REGEX = /^[A-Za-z]{3}\d{6}$/;
// const STAC_REGEX = /^\d{6}[A-Za-z]{3}$/;

export const pacCodeSchema = z
  .string()
  .trim()
  .superRefine((pacCode, ctx) => {
    if (!pacCode || pacCode.length !== 9) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid PAC code'
      });
      return;
    }
    if (PAC_REGEX.test(pacCode)) {
      return;
    }
    // if (STAC_REGEX.test(pacCode)) {
    //   return;
    // }
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Please enter a valid PAC code'
    });
  });

export const ukPhoneNumberSchema = z
  .string()
  .trim()
  .transform((value) => value.replace(/[\s\-()]/g, ''))
  .refine(
    (value) => {
      return /^(07\d{9})$|^(\+?447\d{9})$/.test(value);
    },
    {
      message: 'Please enter a valid UK mobile number'
    }
  )
  .transform((value) => {
    if (value.startsWith('07')) {
      return `44${value.substring(1)}`;
    }
    if (value.startsWith('+44')) {
      return value.substring(1);
    }
    return value;
  });

const isEmptyOrUndefined = (value: string | undefined): boolean => {
  return value === undefined || value === null || value === '';
};

export const numberPortingSchema = z
  .object({
    pacCode: pacCodeSchema,
    incoming_phone_number: ukPhoneNumberSchema,
    // Switching date is optional but if provided must be valid
    // day: z.string().optional(),
    day: z.string({ invalid_type_error: 'Please select a day' }).optional(),
    // month: z.string().optional(),
    month: z.string({ invalid_type_error: 'Please select a month' }).optional(),
    // year: z.string().optional()
    year: z.string({ invalid_type_error: 'Please select a year' }).optional(),
    desired_date: z
      .string({
        required_error: 'Please select a date',
        invalid_type_error: 'Please select a valid date'
      })
      .min(1, 'Please select a date')
  })
  .superRefine((data, ctx) => {
    // Check if any date field has a non-empty value
    const hasDay = !isEmptyOrUndefined(data.day);
    const hasMonth = !isEmptyOrUndefined(data.month);
    const hasYear = !isEmptyOrUndefined(data.year);

    // Only validate date fields if at least one has a non-empty value
    const hasAnyDateField = hasDay || hasMonth || hasYear;

    if (hasAnyDateField) {
      // If any date field is provided with a value, all must be provided with values
      if (!hasDay) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Day is required when providing a switching date',
          path: ['day']
        });
      }

      if (!hasMonth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Month is required when providing a switching date',
          path: ['month']
        });
      }

      if (!hasYear) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Year is required when providing a switching date',
          path: ['year']
        });
      }

      // Only validate the date if all fields have values
      if (hasDay && hasMonth && hasYear) {
        try {
          const day = parseInt(data.day!, 10);
          const month = parseInt(data.month!, 10) - 1; // JS months are 0-indexed
          const year = parseInt(data.year!, 10);

          const date = new Date(year, month, day);

          // Check if the date is valid
          const isValidDate =
            date.getFullYear() === year &&
            date.getMonth() === month &&
            date.getDate() === day;

          if (!isValidDate) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Please select a valid date',
              path: ['day']
            });
          } else if (!isValidPortingDate(date)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message:
                'Please select a valid switching date (must be a business day)',
              path: ['day']
            });
          }
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Please select a valid date',
            path: ['day']
          });
        }
      }
    }
  });

export type NumberPortingFormData = z.infer<typeof numberPortingSchema>;

export const validateNumberPortingForm = (
  formData: unknown
): {
  success: boolean;
  data?: NumberPortingFormData;
  error?: z.ZodError;
} => {
  try {
    // Clean up empty string values for date fields
    if (typeof formData === 'object' && formData !== null) {
      const data = formData as Record<string, unknown>;

      // Convert empty strings to undefined for optional fields
      if (data.day === '') data.day = undefined;
      if (data.month === '') data.month = undefined;
      if (data.year === '') data.year = undefined;
    }

    const validatedData = numberPortingSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const emailSchema = z
  .string()
  .trim()
  .min(1, { message: 'Email address is required' })
  .email({ message: 'Please enter a valid email address' });

export type Email = z.infer<typeof emailSchema>;

export const validateEmailForm = (
  formData: unknown
): {
  success: boolean;
  data?: Email;
  error?: z.ZodError;
} => {
  try {
    const validatedData = emailSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};
export const signInFormSchema = z.object({
  email: emailSchema
});

export type SignInFormData = z.infer<typeof signInFormSchema>;

export const validateSignInForm = (
  formData: unknown
): {
  success: boolean;
  data?: SignInFormData;
  error?: z.ZodError;
} => {
  try {
    const validatedData = signInFormSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const otpSchema = (length: number = 6) => {
  return z
    .string()
    .transform((val) => val.trim())
    .superRefine((val, ctx) => {
      if (!val) {
        ctx.addIssue({
          code: z.ZodIssueCode.too_small,
          minimum: 1,
          type: 'string',
          inclusive: true,
          message: 'Please enter the verification code'
        });
        return;
      }

      const isNumeric = /^\d+$/.test(val);
      if (!isNumeric) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_string,
          validation: 'regex',
          message: 'The verification code must contain only numbers'
        });
      }

      if (val.length !== length) {
        ctx.addIssue({
          code: z.ZodIssueCode.too_big,
          maximum: length,
          type: 'string',
          inclusive: true,
          message: `Please enter all ${length} digits of the verification code`
        });
      }
    });
};

export const passwordSignupSchema = z
  .string()
  .min(8, { message: 'Password must be at least 8 characters long' })
  .refine(
    (value) => {
      return /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/.test(
        value
      );
    },
    {
      message:
        'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character'
    }
  );

export const signupSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: emailSchema,
  password: passwordSignupSchema,
  day: z.string({ invalid_type_error: 'Please select a day' }),
  month: z.string({ invalid_type_error: 'Please select a month' }),
  year: z.string({ invalid_type_error: 'Please select a year' })
});

export const otmSchema = (length: number) =>
  z
    .string()
    .length(
      length,
      `Please enter all ${length} digits of the verification code`
    )
    .regex(/^\d+$/, 'The verification code must contain only numbers');

export const validateOtpData = (
  otp: string,
  length: number
): {
  success: boolean;
  data?: string;
  error?: z.ZodError;
} => {
  try {
    const validatedData = otmSchema(length).parse(otp);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const validateSignUpData = (
  formData: SignupFormData
): {
  success: boolean;
  data?: SignupPayload;
  error?: z.ZodError;
} => {
  try {
    const dataForValidation = {
      ...formData,
      date_of_birth: `${formData.year}-${formData.month}-${formData.day}`
    };

    const validatedData = signupSchema.parse(dataForValidation);
    return { success: true, data: transformToPayload(validatedData) };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

const deliverooRiderSchema = z.object({
  rider_id: z.string().min(1, { message: 'Rider ID is required' }),
  email: emailSchema
});

export const deliverooSignupSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  password: passwordSignupSchema,
  email: emailSchema,
  metadata: deliverooRiderSchema
});

export type DeliverooSignupFormData = z.infer<typeof deliverooSignupSchema>;

export const validateDeliverooSignUpData = (
  formData: DeliverooSignupFormData
): {
  success: boolean;
  data?: DeliverooSignupFormData;
  error?: z.ZodError;
} => {
  try {
    const validatedData = deliverooSignupSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const passwordLoginSchema = z
  .string()
  .nonempty({ message: 'Password is required' });

const loginSchema = z.object({
  email: emailSchema,
  password: passwordLoginSchema
});

export type LoginFormData = z.infer<typeof loginSchema>;

export const validateLoginData = (
  formData: LoginFormData
): {
  success: boolean;
  data?: LoginFormData;
  error?: z.ZodError;
} => {
  try {
    const validatedData = loginSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export type DeliverooRiderFormData = z.infer<typeof deliverooRiderSchema>;

export const validateDeliverooRiderCredentials = (
  formData: DeliverooRiderFormData
): {
  success: boolean;
  data?: DeliverooRiderFormData;
  error?: z.ZodError;
} => {
  try {
    const validatedData = deliverooRiderSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};
