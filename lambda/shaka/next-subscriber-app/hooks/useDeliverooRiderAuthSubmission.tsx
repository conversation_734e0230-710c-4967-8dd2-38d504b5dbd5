import React, { useState } from 'react';
import { useAuth } from '@/auth/hooks/use-auth';
import { useMutation } from '@tanstack/react-query';
import {
  DeliverooRiderFormData,
  validateDeliverooRiderCredentials
} from '@/schema/schema';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { userService } from '@/services/userService';
import { useDeliveryRiderAuth } from '@/src/deliveroo/app/signup/context/DeliveryRiderAuth';
import type { DeliverooAuthResponse } from '@/services/userService';
import useLocalStorage, { LocalKey } from '@/hooks/useLocalStorage';
import { trimWhiteSpace } from '@/utils/formatters';

export function useDeliverooRiderAuthSubmission() {
  const { setIsAuthenticated } = useDeliveryRiderAuth();
  const router = useRouter();
  const [errors, setErrors] = useState<string[]>([]);
  const { apiClient } = useAuth();
  const [, setRiderEmail] = useLocalStorage(LocalKey.RIDER_EMAIL, '');
  const [, setRiderId] = useLocalStorage(LocalKey.RIDER_ID, '');

  const [formData, setFormData] = useState<DeliverooRiderFormData>({
    rider_id: '',
    email: ''
  });

  const {
    mutateAsync: submitAuthForm,
    isPending,
    error: mutationError
  } = useMutation<DeliverooAuthResponse, AxiosError, DeliverooRiderFormData>({
    mutationFn: async (payload) =>
      userService.authenticateRider(apiClient, payload),
    onSuccess: async (data) => {
      if (!data.valid) {
        setErrors(['Invalid rider ID or email']);
      } else {
        router.push(ROUTES_CONFIG['plan-selection'].path);
        setIsAuthenticated(true);
        setRiderEmail(formData.email);
        setRiderId(formData.rider_id);
      }
    },
    onError: (error: AxiosError) => {
      setErrors(standariseNetworkError(error));
    }
  });

  const onSubmit = async (formData: DeliverooRiderFormData) => {
    setErrors([]);
    setFormData(formData);

    const result = validateDeliverooRiderCredentials(formData);
    if (result.success) {
      await submitAuthForm(formData);
      return result;
    } else {
      const formattedErrors = result.error?.format() || {};
      const flatErrors = flattenValidationErrors(formattedErrors);
      setErrors(flatErrors);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const form = e.currentTarget;
    const formValues = new FormData(form);
    const trimmedData: Record<string, string> = {};

    formValues.forEach((value, key) => {
      trimmedData[key] =
        typeof value === 'string' ? trimWhiteSpace(value) : String(value);
    });

    await onSubmit({
      rider_id: trimmedData.id,
      email: trimmedData.email
    });
  };

  return {
    handleFormSubmit,
    isPending,
    errors,
    setErrors,
    mutationError,
    formData
  };
}
