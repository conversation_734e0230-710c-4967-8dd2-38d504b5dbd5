import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';
import { z } from 'zod';

// Schema for the Addon interface
export const addonSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, 'Name is required'),
  region: z.string().min(1, 'Region is required'),
  allowances: z.object({
    data: z.number().nonnegative('Data allowance must be a non-negative number')
  }),
  price: z.number().nonnegative('Price must be a non-negative number')
});

export const familyPlanSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, 'Name is required'),
  price: z.number().nonnegative('Price must be a non-negative number'),
  allowances: z.object({
    data: z.union([z.number(), z.string()]),
    calls: z.string(),
    texts: z.string(),
    europe_data: z
      .number()
      .nonnegative('Europe data must be a non-negative number')
  }),
  travel_addons: z.array(addonSchema).optional()
});

export const mainPlanSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, 'Name is required'),
  allowances: z.object({
    data: z.union([z.number(), z.string()]),
    calls: z.string(),
    texts: z.string(),
    europe_data: z
      .number()
      .nonnegative('Europe data must be a non-negative number')
  }),
  travel_addons: z.array(addonSchema),
  price: z.number().nonnegative('Price must be a non-negative number'),
  family_plans: z.array(familyPlanSchema).optional()
});

export const plansApiResponseSchema = z.array(mainPlanSchema);

export const simsApiResponseSchema = z.array(
  z.object({
    serial_number: z.string(),
    status: z.string(),
    activation_date: z.string().nullable(),
    current_msisdn: z.string(),
    sim_type: z.enum(['physical', 'esim']),
    correlation_id: z.string(),
    service_type: z.string(),
    esim_data: z.object({
      qr_code_base64: z.string().base64(),
      qr_code_image: z.string().url(),
      sm_dp_address: z.string(),
      activation_code: z.string(),
      ios_universal_link: z.string().url(),
      android_activation_data: z.string()
    })
  })
);

const PlanDetailsSchema = z.object({
  data_allowance_gb: z.number().int().nonnegative(),
  voice_allowance_minutes: z.number().int().nonnegative(),
  sms_allowance: z.number().int().nonnegative(),
  eu_roaming_enabled: z.boolean(),
  row_roaming_enabled: z.boolean(),
  eu_data_allowance_gb: z.number().int().nonnegative(),
  row_data_allowance_gb: z.number().int().nonnegative(),
  bundle_id: z.string()
});

const PlanSchema = z.object({
  id: z.number().int().positive(),
  name: z.string(),
  plan_details: PlanDetailsSchema,
  reference_id: z.string().nullable()
});

const UsageDataSchema = z.object({
  used: z.number(),
  remaining: z.number().nullable()
});

const RegionalUsageSchema = z.object({
  data: UsageDataSchema,
  voice: UsageDataSchema,
  sms: UsageDataSchema
});

const RoamingUsageSchema = z.object({
  daysUsed: z.number().int().nonnegative(),
  daysAllowed: z.number().int().positive()
});

const UsageSchema = z.object({
  uk: RegionalUsageSchema,
  europe: RegionalUsageSchema.optional(),
  period_start: z.string().datetime(),
  period_end: z.string().datetime(),
  billing_cycle_period: z.enum(['monthly', 'yearly'])
});

const ESimDataSchema = z.object({
  qr_code_base64: z.string(),
  qr_code_image: z.string().url(),
  sm_dp_address: z.string(),
  activation_code: z.string(),
  ios_universal_link: z.string().url(),
  android_activation_data: z.string()
});

const CurrentSimSchema = z.object({
  serial_number: z.string().length(20),
  status: z.enum(['active', 'inactive', 'suspended']),
  activation_date: z.string().datetime().nullable(),
  current_msisdn: z.string(),
  sim_type: z.enum(['physical', 'esim']),
  service_type: z.string(),
  esim_data: ESimDataSchema.nullable()
});

const SubscriptionSchema = z.object({
  id: z.number().int().positive(),
  subscriber: z.number().int().positive(),
  current_plan: PlanSchema,
  usage: UsageSchema,
  roamingUsage: RoamingUsageSchema,
  current_sim: CurrentSimSchema,
  current_msisdn: z.string(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime().nullable(),
  status: z.string(),
  service_type: z.string(),
  current_billing_cycle_end: z.string().datetime(),
  next_billing_cycle_start: z.string().datetime(),
  billing_cycle_period: z.enum(['monthly', 'yearly']),
  reference_id: z.string().nullable(),
  correlation_id: z.string()
});

export const SubscriptionsSchema = z.array(SubscriptionSchema);

// Type inference from the schema
export type Addon = z.infer<typeof addonSchema>;
// export type FamilyPlan = z.infer<typeof familyPlanSchema>;
export type MainPlan = z.infer<typeof mainPlanSchema>;
export type PlansApiResponse = z.infer<typeof plansApiResponseSchema>;
export type SimsApiResponse = z.infer<typeof simsApiResponseSchema>;
export type SubscriptionsApiResponse = z.infer<typeof SubscriptionsSchema>;
export type SingleSubscription = z.infer<typeof SubscriptionSchema>;
// export type RoamingUsage = z.infer<typeof RoamingUsageSchema>;

// Function to check if a date is a valid switching date
// (not a weekend or UK public holiday)
export const isValidPortingDate = (date: Date): boolean => {
  // Check if it's a weekend
  const day = date.getDay();
  if (day === 0 || day === 6) {
    return false; // Weekend (Sunday = 0, Saturday = 6)
  }

  // Format date as YYYY-MM-DD for holiday checking
  const formattedDate = date.toISOString().split('T')[0];

  // Check if it's a public holiday
  return !UK_PUBLIC_HOLIDAYS.includes(formattedDate);
};

const planNameSchema = z
  .string()
  .min(1, 'Plan name is required')
  .refine((val) => val.length <= 10, {
    message: 'Plan name cannot be longer than 10 characters'
  });

export const validatePlanNameForm = (
  formData: unknown
): {
  success: boolean;
  data?: string;
  error?: z.ZodError;
} => {
  try {
    const validatedData = planNameSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export type PlanNameFormData = z.infer<typeof planNameSchema>;
