import { describe, it, expect } from 'vitest';
import {
  formatPortingCode,
  getNextWorkingDay,
  getAvailableDatesRange,
  getStepFromPath,
  createStepRoutes,
  flattenValidationErrors,
  selectAvailableId,
  availableMainPlanIds,
  availableFamilyPlanIds
} from '@/utils/helpers';
import type { ValidationErrorObject } from '@/utils/helpers';
import type { PurchaseIntentDetails } from '@/services/paymentService';
import { validateNumberPortingForm } from '@/schema/schema';
import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';
import { getSignupPageName } from '@/src/uswitch/utils/helpers';
import { getDayMonthYearFromDate } from '@/utils/formatters';
import { SimsApiResponse } from '@/schemas/schemas';

const getDateString = (date: Date) => date.toISOString().slice(0, 10);

describe('formatPortingCode', () => {
  it('removes spaces and converts to uppercase', () => {
    expect(formatPortingCode('pac 123 456 789')).toBe('PAC123456789');
    expect(formatPortingCode('pac1 23 456 78 9')).toBe('PAC123456789');
  });

  describe('plan reconciliation helpers', () => {
    describe('selectAvailableId', () => {
      it('returns storedId when it is available', () => {
        expect(selectAvailableId(5, [1, 5, 9])).toBe(5);
      });

      it('returns fallbackId when storedId is invalid and fallbackId is available', () => {
        expect(selectAvailableId(10, [2, 3, 4], 3)).toBe(3);
      });

      it('returns first available when neither storedId nor fallbackId are available', () => {
        expect(selectAvailableId(10, [7, 8, 9], 99)).toBe(7);
      });

      it('returns storedId when availableIds is empty', () => {
        expect(selectAvailableId(42, [], 1)).toBe(42);
      });

      it('returns fallbackId when storedId is undefined and availableIds is empty', () => {
        expect(selectAvailableId(undefined, [], 11)).toBe(11);
      });

      it('returns first available when storedId is undefined and list is non-empty', () => {
        expect(selectAvailableId(undefined, [4, 5, 6], 1)).toBe(4);
      });
    });

    describe('availableMainPlanIds', () => {
      it('extracts ids from planData', () => {
        const planData = [{ id: 4 }, { id: 5 }] as any;
        expect(availableMainPlanIds(planData)).toEqual([4, 5]);
      });

      it('returns empty array for undefined or empty input', () => {
        expect(availableMainPlanIds(undefined)).toEqual([]);
        expect(availableMainPlanIds([] as any)).toEqual([]);
      });
    });

    describe('availableFamilyPlanIds', () => {
      it('extracts family plan ids from nested planData', () => {
        const planData = [
          { id: 1, family_plans: [{ id: 10 }, { id: 11 }] },
          { id: 2, family_plans: [{ id: 20 }] }
        ] as any;
        expect(availableFamilyPlanIds(planData)).toEqual([10, 11, 20]);
      });

      it('handles missing or empty family_plans arrays', () => {
        const planData = [{ id: 1 }, { id: 2, family_plans: [] }] as any;
        expect(availableFamilyPlanIds(planData)).toEqual([]);
      });

      it('returns empty array for undefined input', () => {
        expect(availableFamilyPlanIds(undefined)).toEqual([]);
      });
    });
  });

  it('handles already formatted codes', () => {
    expect(formatPortingCode('PAC123456789')).toBe('PAC123456789');
  });

  it('handles mixed case input', () => {
    expect(formatPortingCode('PaC123456789')).toBe('PAC123456789');
  });

  it('returns original if format is invalid', () => {
    // Too short
    expect(formatPortingCode('PAC123')).toBe('PAC123');
    // Wrong prefix
    expect(formatPortingCode('ABC123456789')).toBe('ABC123456789');
    // Contains non-alphanumeric
    expect(formatPortingCode('PAC-123-456-789')).toBe('PAC-123-456-789');
  });

  it('handles empty string', () => {
    expect(formatPortingCode('')).toBe('');
  });
});

describe('getNextWorkingDay', () => {
  it('returns the next weekday if no weekend or holiday', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-24'),
      UK_PUBLIC_HOLIDAYS
    );
    expect(getDateString(result)).toBe('2025-12-29'); // 25th and 26th are holidays, 27th/28th are weekend, 29th is Monday
  });

  it('skips weekends', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-19'),
      UK_PUBLIC_HOLIDAYS
    ); // Friday
    expect(getDateString(result)).toBe('2025-12-22'); // Monday
  });

  it('skips holidays', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-24'),
      UK_PUBLIC_HOLIDAYS
    );
    expect(getDateString(result)).toBe('2025-12-29'); // 25th, 26th are holidays, 27th/28th are weekend
  });

  it('returns the next working day if input is a holiday', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-25'),
      UK_PUBLIC_HOLIDAYS
    );
    expect(getDateString(result)).toBe('2025-12-29');
  });

  it('returns the next working day if input is a weekend', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-27'),
      UK_PUBLIC_HOLIDAYS
    ); // Saturday
    expect(getDateString(result)).toBe('2025-12-29'); // Monday
  });
});

describe('getAvailableDatesRange', () => {
  it('returns the correct number of working days', () => {
    const result = getAvailableDatesRange(
      new Date('2025-12-22'),
      5,
      UK_PUBLIC_HOLIDAYS
    );
    expect(result).toHaveLength(5);
  });

  it('skips weekends and holidays', () => {
    const result = getAvailableDatesRange(
      new Date('2025-12-24'),
      3,
      UK_PUBLIC_HOLIDAYS
    );
    const isoDates = result.map(getDateString);
    expect(isoDates).toEqual(['2025-12-29', '2025-12-30', '2025-12-31']);
  });

  it('returns empty array if 0 days requested', () => {
    const result = getAvailableDatesRange(
      new Date('2025-12-22'),
      0,
      UK_PUBLIC_HOLIDAYS
    );
    expect(result).toEqual([]);
  });

  it('works with no holidays', () => {
    const result = getAvailableDatesRange(new Date('2025-12-22'), 2);
    const isoDates = result.map(getDateString);
    expect(isoDates).toEqual(['2025-12-23', '2025-12-24']);
  });
});

describe('validateNumberPortingForm', () => {
  it('validates a correct form (no date)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      desired_date: '2025-08-07'
    });
    expect(result.success).toBe(true);
    expect(result.data).toMatchObject({
      pacCode: 'PAC123456',
      incoming_phone_number: '447123456789',
      desired_date: '2025-08-07'
    });
  });

  it('validates a correct form (with valid business day date)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '+447123456789',
      day: '15',
      month: '6',
      year: '2025',
      desired_date: '2025-06-15'
    });

    const validResult = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '+447123456789',
      day: '16',
      month: '6',
      year: '2025',
      desired_date: '2025-06-16'
    });

    expect(validResult.success).toBe(true);
    expect(validResult.data).toMatchObject({
      pacCode: 'PAC123456',
      incoming_phone_number: '447123456789',
      day: '16',
      month: '6',
      year: '2025'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      // @ts-expect-error tests
      result.error.issues.some((i) => i.message.includes('business day'))
    ).toBe(true);
  });

  it('fails on invalid PAC code', () => {
    const result = validateNumberPortingForm({
      pacCode: '123456',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    // @ts-expect-error tests
    expect(result.error.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails on invalid phone number', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********' // landline
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      // @ts-expect-error tests
      result.error.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });

  it('fails if only some date fields are present', () => {
    const missingDay = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      month: '2',
      year: '2025',
      desired_date: '2025-06-15'
    });
    expect(missingDay.success).toBe(false);
    // @ts-expect-error tests
    expect(missingDay.error.issues.some((i) => i.path.includes('day'))).toBe(
      true
    );

    const missingMonth = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '1',
      year: '2025',
      desired_date: '2025-06-15'
    });
    expect(missingMonth.success).toBe(false);
    expect(
      // @ts-expect-error tests
      missingMonth.error.issues.some((i) => i.path.includes('month'))
    ).toBe(true);

    const missingYear = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '1',
      month: '2',
      desired_date: '2025-06-15'
    });
    expect(missingYear.success).toBe(false);
    // @ts-expect-error tests
    expect(missingYear.error.issues.some((i) => i.path.includes('year'))).toBe(
      true
    );
  });

  it('fails on invalid date (e.g., Feb 30)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '30',
      month: '2',
      year: '2025',
      desired_date: '2025-02-30'
    });
    expect(result.success).toBe(false);
    expect(
      // @ts-expect-error tests
      result.error.issues.some((i) => i.message.includes('valid date'))
    ).toBe(true);
  });

  it('accepts empty date fields as undefined', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '',
      month: '',
      year: '',
      desired_date: '2025-06-15'
    });
    expect(result.success).toBe(true);
    expect(result.data).toMatchObject({
      pacCode: 'PAC123456',
      incoming_phone_number: '447123456789'
    });
  });

  it('fails on extra fields (should ignore or error)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      foo: 'bar',
      desired_date: '2025-06-15'
    });
    expect(result.success).toBe(true);
    expect(result.data).not.toHaveProperty('foo');
  });

  it('fails gracefully on null/undefined/empty input', () => {
    expect(validateNumberPortingForm(null).success).toBe(false);
    expect(validateNumberPortingForm(undefined).success).toBe(false);
    expect(validateNumberPortingForm({}).success).toBe(false);
  });

  it('fails if code is more than 9 characters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC1234567', // 10 chars
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is less than 9 characters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC12345', // 8 chars
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code has special characters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC!23456',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code has spaces', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC 123456',
      phoneNumber: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is all digits', () => {
    const result = validateNumberPortingForm({
      pacCode: '123456789',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is all letters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'ABCDEFGHI',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is blank or whitespace', () => {
    const result = validateNumberPortingForm({
      pacCode: '   ',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if phone number contains letters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '07123abc789'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      result.error?.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });

  it('fails if phone number is too short', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '07123'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      result.error?.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });

  it('fails if date fields are non-numeric', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: 'xx',
      month: '6',
      year: '2025',
      desired_date: ''
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      result.error?.issues.some((i) => i.message.includes('valid date'))
    ).toBe(true);
  });

  it('fails if all fields are empty', () => {
    const result = validateNumberPortingForm({
      pacCode: '',
      incoming_phone_number: '',
      day: '',
      month: '',
      year: ''
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('fails if code and phone number are both invalid', () => {
    const result = validateNumberPortingForm({
      pacCode: '123',
      incoming_phone_number: 'abc'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
    expect(
      result.error?.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });
});

describe('getSignupPageName', () => {
  it('extracts and formats the page name from pathname', () => {
    expect(getSignupPageName('/signup/plan-selection')).toBe('Plan Selection');
    expect(getSignupPageName('/signup/add-ons')).toBe('Add Ons');
    expect(getSignupPageName('/signup/number-porting')).toBe('Number Porting');
  });

  it('handles underscores and hyphens', () => {
    expect(getSignupPageName('/signup/plan_selection')).toBe('Plan Selection');
    expect(getSignupPageName('/signup/plan-selection_extra')).toBe(
      'Plan Selection Extra'
    );
  });

  it('returns empty string if no segment after signup', () => {
    expect(getSignupPageName('/signup/')).toBe('');
    expect(getSignupPageName('/signup')).toBe('');
  });

  it('ignores query and hash', () => {
    expect(getSignupPageName('/signup/plan-selection?foo=bar')).toBe(
      'Plan Selection'
    );
    expect(getSignupPageName('/signup/plan-selection#section')).toBe(
      'Plan Selection'
    );
  });

  it('returns empty string for unrelated routes', () => {
    expect(getSignupPageName('/dashboard')).toBe('');
    expect(getSignupPageName('/')).toBe('');
  });

  it('returns empty string for invalid or non-string inputs', () => {
    // @ts-expect-error test
    expect(getSignupPageName(null)).toBe('');
    // @ts-expect-error test
    expect(getSignupPageName(undefined)).toBe('');
    // @ts-expect-error test
    expect(getSignupPageName(123)).toBe('');
    expect(getSignupPageName('')).toBe('');
  });
});

describe('getStepFromPath', () => {
  it('should return the correct step key for matching pathname', () => {
    const stepRoutes = {
      step1: 'profile',
      step2: 'settings',
      step3: 'billing'
    };

    const result = getStepFromPath(stepRoutes, '/account/profile');
    expect(result).toBe('step1');
  });

  it('should handle multi-segment paths correctly', () => {
    const stepRoutes = {
      dashboard: 'overview',
      settings: 'preferences',
      profile: 'edit'
    };

    const result = getStepFromPath(stepRoutes, '/app/user/account/preferences');
    expect(result).toBe('settings');
  });

  it('should handle various route configurations', () => {
    const stepRoutes = {
      'onboarding-step-1': 'welcome',
      user_profile: 'profile-edit',
      'final-step': 'complete'
    };

    const result1 = getStepFromPath(stepRoutes, '/onboarding/welcome');
    const result2 = getStepFromPath(stepRoutes, '/user/profile-edit');
    const result3 = getStepFromPath(stepRoutes, '/flow/complete');

    expect(result1).toBe('onboarding-step-1');
    expect(result2).toBe('user_profile');
    expect(result3).toBe('final-step');
  });

  it('should return undefined for null or undefined pathname', () => {
    const stepRoutes = {
      step1: 'profile',
      step2: 'settings'
    };

    const result1 = getStepFromPath(stepRoutes, null);
    // @ts-expect-error test
    const result2 = getStepFromPath(stepRoutes, undefined);

    expect(result1).toBeUndefined();
    expect(result2).toBeUndefined();
  });

  it('should return undefined for unmatched pathname', () => {
    const stepRoutes = {
      step1: 'profile',
      step2: 'settings'
    };

    const result = getStepFromPath(stepRoutes, '/account/nonexistent');
    expect(result).toBeUndefined();
  });

  it('should handle empty and root path scenarios', () => {
    const stepRoutes = {
      home: '',
      step1: 'profile'
    };

    const result1 = getStepFromPath(stepRoutes, '');
    const result2 = getStepFromPath(stepRoutes, '/');
    const result3 = getStepFromPath(stepRoutes, '///');

    expect(result1).toBe('home');
    expect(result2).toBe('home');
    expect(result3).toBe('home');
  });
});

describe('createStepRoutes', () => {
  it('creates a correct mapping with a single step', () => {
    const steps = ['profile'] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({ profile: 'profile' });
  });

  it('creates a correct mapping with multiple steps', () => {
    const steps = ['profile', 'settings', 'billing'] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({
      profile: 'profile',
      settings: 'settings',
      billing: 'billing'
    });
  });

  it('preserves the step order in the returned object', () => {
    const steps = ['step3', 'step1', 'step2'] as const;
    const result = createStepRoutes(steps);
    const keys = Object.keys(result);
    expect(keys).toEqual(['step3', 'step1', 'step2']);
  });

  it('returns an empty object for an empty array', () => {
    const steps = [] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({});
  });

  it('handles steps with special characters and spaces', () => {
    const steps = [
      'step-with-dashes',
      'step_with_underscores',
      'step with spaces',
      'step@special#chars'
    ] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({
      'step-with-dashes': 'step-with-dashes',
      step_with_underscores: 'step_with_underscores',
      'step with spaces': 'step with spaces',
      'step@special#chars': 'step@special#chars'
    });
  });

  it('handles duplicate step names correctly', () => {
    const steps = ['profile', 'settings', 'profile'] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({
      profile: 'profile',
      settings: 'settings'
    });
    expect(Object.keys(result)).toHaveLength(2);
  });
});

describe('flattenValidationErrors', () => {
  it('flattens nested validation errors with parent keys', () => {
    const errors: ValidationErrorObject = {
      user: {
        name: {
          _errors: ['Name is required', 'Name must be at least 2 characters']
        },
        email: {
          _errors: ['Invalid email format']
        }
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([
      'name: Name is required',
      'name: Name must be at least 2 characters',
      'email: Invalid email format'
    ]);
  });

  it('processes root level validation errors', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root level error 1', 'Root level error 2']
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Root level error 1', 'Root level error 2']);
  });

  it('combines errors from multiple nested levels', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root error'],
      level1: {
        _errors: ['Level 1 error'],
        level2: {
          _errors: ['Level 2 error'],
          level3: {
            _errors: ['Level 3 error']
          }
        }
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([
      'Root error',
      'level1: Level 1 error',
      'level2: Level 2 error',
      'level3: Level 3 error'
    ]);
  });

  it('handles empty validation error object', () => {
    const errors: ValidationErrorObject = {};

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });

  it('processes validation object with null values', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root error'],
      nullField: null,
      undefinedField: undefined,
      validField: {
        _errors: ['Valid field error']
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Root error', 'validField: Valid field error']);
  });

  it('test_handles_empty_errors_array', () => {
    const errors: ValidationErrorObject = {
      _errors: [],
      field: {
        _errors: []
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });

  it('test_flatten_validation_errors_with_parent_key_prefixes', () => {
    const errors: ValidationErrorObject = {
      name: {
        _errors: ['Name is required']
      },
      address: {
        street: {
          _errors: ['Street is required']
        }
      }
    };

    const result = flattenValidationErrors(errors, 'form');

    expect(result).toEqual([
      'name: Name is required',
      'street: Street is required'
    ]);
  });

  it('test_flatten_nested_validation_error_objects_recursively', () => {
    const errors: ValidationErrorObject = {
      user: {
        profile: {
          personal: {
            name: {
              _errors: ['Deep nested error']
            }
          }
        }
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['name: Deep nested error']);
  });

  it('test_flatten_validation_errors_without_parent_key', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Error without parent key'],
      field: {
        _errors: ['Field error']
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Error without parent key', 'field: Field error']);
  });

  it('test_flatten_empty_validation_error_object', () => {
    const errors: ValidationErrorObject = {};

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });

  it('test_flatten_validation_errors_with_null_nested_values', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root error'],
      nullValue: null,
      validNested: {
        _errors: ['Nested error']
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Root error', 'validNested: Nested error']);
  });

  it('test_flatten_validation_errors_with_empty_errors_array', () => {
    const errors: ValidationErrorObject = {
      _errors: [],
      nested: {
        _errors: []
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });
});

describe('getDayMonthYearFromDate', () => {
  it('parses valid date string', () => {
    expect(getDayMonthYearFromDate('2024-06-01')).toEqual({
      year: '2024',
      month: '06',
      day: '01'
    });
  });
  it('handles invalid date string', () => {
    expect(getDayMonthYearFromDate('2024-06')).toEqual({
      year: '2024',
      month: '06',
      day: undefined
    });
  });
  it('handles empty string', () => {
    expect(getDayMonthYearFromDate('')).toEqual({
      year: '',
      month: '',
      day: ''
    });
  });
});
