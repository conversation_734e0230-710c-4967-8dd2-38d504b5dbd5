import React from 'react';

export interface IconProps {
  className?: string;
  size?: number;
  stroke?: string;
}

// TODO - make more customisable
export function CoverageIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <g clipPath="url(#clip0_93_16620)">
        <path
          d="M7.27158 4.17502C5.52291 5.50283 4.38462 7.66879 4.38462 10.1172C4.38462 12.5656 5.52291 14.7316 7.27158 16.0594L6.45919 17.2344C4.36446 15.6438 3 13.0478 3 10.1172C3 7.18659 4.36446 4.59056 6.45919 3L7.27158 4.17502Z"
          fill="#434343"
        />
        <path
          d="M19.6154 10.1172C19.6154 7.66879 18.4771 5.50283 16.7284 4.17502L17.5408 3C19.6355 4.59056 21 7.18659 21 10.1172C21 13.0478 19.6355 15.6438 17.5408 17.2344L16.7284 16.0594C18.4771 14.7316 19.6154 12.5656 19.6154 10.1172Z"
          fill="#434343"
        />
        <path
          d="M7.15385 10.1172C7.15385 8.90561 7.73824 7.80488 8.6883 7.07462L7.86939 5.90456C6.60364 6.87748 5.76923 8.39389 5.76923 10.1172C5.76923 11.8405 6.60364 13.3569 7.86939 14.3298L8.6883 13.1598C7.73824 12.4295 7.15385 11.3288 7.15385 10.1172Z"
          fill="#434343"
        />
        <path
          d="M15.3117 7.07462C16.2618 7.80488 16.8462 8.90561 16.8462 10.1172C16.8462 11.3288 16.2618 12.4295 15.3117 13.1598L16.1306 14.3298C17.3964 13.3569 18.2308 11.8405 18.2308 10.1172C18.2308 8.39389 17.3964 6.87748 16.1306 5.90456L15.3117 7.07462Z"
          fill="#434343"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 7.21512C10.4706 7.21512 9.23077 8.51442 9.23077 10.1172C9.23077 11.4694 10.1133 12.6057 11.3077 12.9278L11.3077 21H12.6923L12.6923 12.9278C13.8867 12.6057 14.7692 11.4694 14.7692 10.1172C14.7692 8.51442 13.5294 7.21512 12 7.21512ZM10.6154 10.1172C10.6154 9.31581 11.2353 8.66616 12 8.66616C12.7647 8.66616 13.3846 9.31581 13.3846 10.1172C13.3846 10.9186 12.7647 11.5682 12 11.5682C11.2353 11.5682 10.6154 10.9186 10.6154 10.1172Z"
          fill="#434343"
        />
      </g>
      <defs>
        <clipPath id="clip0_93_16620">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function PlusIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M11.25 12.75V20.25H12.75V12.75H20.25V11.25H12.75V3.75H11.25V11.25H3.75V12.75H11.25Z"
        fill="white"
      />
    </svg>
  );
}

export function MinusIcon({ className }: { className?: string }) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <rect x="2.5" y="7.5" width="11" height="1.5" fill="white" />
    </svg>
  );
}

export function CloseIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M16.205 18.0249L25.59 27.4099L27.4099 25.59L18.0249 16.205L27.41 6.81992L25.5901 5L16.205 14.3851L6.81992 5L5 6.81992L14.3851 16.205L5.00006 25.59L6.81998 27.4099L16.205 18.0249Z"
        fill="#141414"
      />
    </svg>
  );
}

export function TickIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M7.18468 14.0036L7.18342 14.0048L8.12643 14.9478L17.1313 5.94301L16.1882 5L8.12769 13.0606L4.06801 9.00088L3.125 9.94389L7.18468 14.0036Z"
        fill="#141414"
      />
    </svg>
  );
}

export function TickIconGreen() {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M7.18468 14.0036L7.18342 14.0048L8.12643 14.9478L17.1313 5.94301L16.1882 5L8.12769 13.0606L4.06801 9.00088L3.125 9.94389L7.18468 14.0036Z"
        fill="#00A13E"
      />
    </svg>
  );
}

export function ChevronDown({
  className,
  size = 24
}: {
  className?: string;
  size?: number;
}) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 16.8105L3.96966 8.78022L5.03032 7.71956L12 14.6892L18.9697 7.71956L20.0303 8.78022L12 16.8105Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ChevronUp() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 7.18945L20.0303 15.2198L18.9697 16.2804L12 9.31077L5.03033 16.2804L3.96967 15.2198L12 7.18945Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ChevronRight({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.4141 16.0001L11.707 26.7072L10.2927 25.293L19.5856 16.0001L10.2927 6.70718L11.707 5.29297L22.4141 16.0001Z"
        fill="#141414"
      />
    </svg>
  );
}

export function PlayButtonIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.375 10.8333L7.5 14.375V6.25L14.375 10.8333ZM8.75 12.3249V8.58565L11.914 10.695L8.75 12.3249Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 1.25C5.16751 1.25 1.25 5.16751 1.25 10C1.25 14.8325 5.16751 18.75 10 18.75C14.8325 18.75 18.75 14.8325 18.75 10C18.75 5.16751 14.8325 1.25 10 1.25ZM2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5C5.85786 17.5 2.5 14.1421 2.5 10Z"
        fill="#141414"
      />
    </svg>
  );
}

export function PACIcon({
  className,
  fill = '#E5F3FF'
}: {
  className?: string;
  fill?: string;
}) {
  return (
    <svg
      className={className}
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <rect width="40" height="40" rx="2" fill={fill} />
      <path d="M26 17L14 17V15L26 15V17Z" fill="#141414" />
      <path d="M14 21L26 21V19L14 19V21Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 8H33V28H25.4142L20 33.4142L14.5858 28H7V8ZM9 10V26H15.4142L20 30.5858L24.5858 26H31V10H9Z"
        fill="#141414"
      />
    </svg>
  );
}

export function TextMessageIcon({ fill = '#E5F3FF' }: { fill?: string }) {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <rect width="40" height="40" rx="2" fill={fill} />
      <path d="M24 7H7V33H24V26H22V31H9V9H24V7Z" fill="#141414" />
      <path d="M17 29H14V27H17V29Z" fill="#141414" />
      <path d="M22 17L29 17V15L22 15V17Z" fill="#141414" />
      <path d="M22 20V18H26V20H22Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 11V27L21.6 24H33V11H18ZM31 22H20.8759L20 22.7299V13H31V22Z"
        fill="#141414"
      />
    </svg>
  );
}

export function UserIcon({ className, size = 24 }: IconProps) {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_620_3153)">
        <path
          d="M3.49634 23.0545V18.8029H7.74802"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M22.8667 9.96875C23.9245 15.5945 20.4819 21.1718 14.8611 22.6778C10.5591 23.8305 6.1697 22.28 3.49634 19.0674"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.13332 14.0315C0.0754299 8.40578 3.51814 2.82849 9.13892 1.32241C13.4406 0.16978 17.8297 1.72006 20.5031 4.93219"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M20.5032 0.945679V5.19737H16.2515"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 11.1429C13.1835 11.1429 14.1429 10.1835 14.1429 9.00003C14.1429 7.81657 13.1835 6.85718 12 6.85718C10.8166 6.85718 9.85718 7.81657 9.85718 9.00003C9.85718 10.1835 10.8166 11.1429 12 11.1429Z"
          stroke="black"
          strokeWidth="1.5"
        />
        <path
          d="M8.0708 16.2858C8.73214 14.7722 10.2425 13.7144 11.9999 13.7144C13.7573 13.7144 15.2677 14.7722 15.929 16.2858"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_620_3153">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ErrorIcon({
  className,
  size = 24,
  stroke = 'currentColor'
}: IconProps) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={stroke}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      aria-hidden="true"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" x2="12" y1="8" y2="12"></line>
      <line x1="12" x2="12.01" y1="16" y2="16"></line>
    </svg>
  );
}

export function ErrorIconFilled({ className, size = 24 }: IconProps) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M10 1.25C14.8325 1.25 18.75 5.16751 18.75 10C18.75 14.8325 14.8325 18.75 10 18.75C5.16751 18.75 1.25 14.8325 1.25 10C1.25 5.16751 5.16751 1.25 10 1.25ZM10 11.875C9.30964 11.875 8.75 12.4346 8.75 13.125C8.75 13.8154 9.30964 14.375 10 14.375C10.6904 14.375 11.25 13.8154 11.25 13.125C11.25 12.4346 10.6904 11.875 10 11.875ZM9.375 5.625V10.625H10.625V5.625H9.375Z"
        fill="#EA0040"
      />
    </svg>
  );
}

export function LoadingSpinner({
  className,
  size = 25
}: {
  className?: string;
  size?: number;
}) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 100 100"
      aria-hidden="true"
    >
      <circle
        cx="50"
        cy="50"
        fill="none"
        opacity={0.7}
        stroke="currentColor"
        strokeWidth="10"
        r="35"
        strokeDasharray="164.93361431346415 56.97787143782138"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          repeatCount="indefinite"
          dur="1s"
          values="0 50 50;360 50 50"
          keyTimes="0;1"
        ></animateTransform>
      </circle>
    </svg>
  );
}

export function InfoIcon({ className, size = 24 }: IconProps) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_620_3035)">
        <path
          d="M12 23.1429C18.1541 23.1429 23.1429 18.1541 23.1429 12C23.1429 5.84601 18.1541 0.857178 12 0.857178C5.84601 0.857178 0.857178 5.84601 0.857178 12C0.857178 18.1541 5.84601 23.1429 12 23.1429Z"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M9.42871 17.1429H14.5716"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.9999 17.1429V11.1429H10.2856"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.0001 7.28573C11.7634 7.28573 11.5715 7.09385 11.5715 6.85716C11.5715 6.62047 11.7634 6.42859 12.0001 6.42859"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 7.28573C12.2367 7.28573 12.4286 7.09385 12.4286 6.85716C12.4286 6.62047 12.2367 6.42859 12 6.42859"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_620_3035">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function WifiIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="57"
      height="57"
      viewBox="0 0 57 57"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <rect x="0.5" y="0.5" width="56" height="56" rx="2" fill="#E4FBED" />
      <path
        d="M16.2064 24.5796C22.9958 17.8068 34.0046 17.8068 40.7939 24.5796L42.2064 23.1636C34.6364 15.6121 22.364 15.6121 14.7939 23.1636L16.2064 24.5796Z"
        fill="#141414"
      />
      <path
        d="M20.0299 28.3938C24.7076 23.7275 32.2927 23.7275 36.9704 28.3938L38.3829 26.9779C32.9245 21.5328 24.0758 21.5328 18.6175 26.9779L20.0299 28.3938Z"
        fill="#141414"
      />
      <path
        d="M23.8535 32.208C26.4195 29.6482 30.5808 29.6482 33.1469 32.208L34.5593 30.792C31.2127 27.4535 25.7877 27.4535 22.441 30.792L23.8535 32.208Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.5002 33.5C26.8433 33.5 25.5002 34.8431 25.5002 36.5C25.5002 38.1569 26.8433 39.5 28.5002 39.5C30.157 39.5 31.5002 38.1569 31.5002 36.5C31.5002 34.8431 30.157 33.5 28.5002 33.5ZM27.5002 36.5C27.5002 35.9477 27.9479 35.5 28.5002 35.5C29.0525 35.5 29.5002 35.9477 29.5002 36.5C29.5002 37.0523 29.0525 37.5 28.5002 37.5C27.9479 37.5 27.5002 37.0523 27.5002 36.5Z"
        fill="#141414"
      />
    </svg>
  );
}

export function CameraIcon({
  className,
  fill = '#E4FBED'
}: {
  className?: string;
  fill?: string;
}) {
  return (
    <svg
      className={className}
      width="57"
      height="57"
      viewBox="0 0 57 57"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <rect x="0.5" y="0.5" width="56" height="56" rx="2" fill={fill} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.5 24.5C25.7386 24.5 23.5 26.7386 23.5 29.5C23.5 32.2614 25.7386 34.5 28.5 34.5C31.2614 34.5 33.5 32.2614 33.5 29.5C33.5 26.7386 31.2614 24.5 28.5 24.5ZM25.5 29.5C25.5 27.8431 26.8431 26.5 28.5 26.5C30.1569 26.5 31.5 27.8431 31.5 29.5C31.5 31.1569 30.1569 32.5 28.5 32.5C26.8431 32.5 25.5 31.1569 25.5 29.5Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M33.118 16.5H23.882L21.882 20.5H15.5V40.5H41.5V20.5H35.118L33.118 16.5ZM17.5 22.5H23.118L25.118 18.5H31.882L33.882 22.5H39.5V38.5H17.5V22.5Z"
        fill="#141414"
      />
    </svg>
  );
}

export function SimIcon({
  className,
  fill = '#E4FBED'
}: {
  className?: string;
  fill?: string;
}) {
  return (
    <svg
      className={className}
      width="57"
      height="57"
      viewBox="0 0 57 57"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <rect x="0.5" y="0.5" width="56" height="56" rx="2" fill={fill} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M30.6716 17.5H21.5V39.5H35.5V22.3284L30.6716 17.5ZM31.5 15.5H19.5V41.5H37.5V21.5L31.5 15.5Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.5 27.5V37.5H33.5V27.5H23.5ZM31.5 29.5H29.5V31.5H31.5V29.5ZM31.5 33.5H29.5V35.5H31.5V33.5ZM25.5 29.5H27.5V35.5H25.5V29.5Z"
        fill="#141414"
      />
    </svg>
  );
}

export function MiniGridIcon() {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.4131 4.4502H4.66309V11.2002H11.4131V4.4502Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.4131 14.2002H4.66309V20.9502H11.4131V14.2002Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4131 4.4502H21.1631V11.2002H14.4131V4.4502Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.1631 14.2002H14.4131V20.9502H21.1631V14.2002Z"
        fill="#141414"
      />
    </svg>
  );
}

export function StatsIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M15.7498 3.75H19.1891L13.5363 9.40277L8.32538 3.44738L2.51172 8.43052L3.48791 9.56941L8.17424 5.55255L13.4633 11.5972L20.2498 4.81061V8.25H21.7498V2.25H15.7498V3.75Z"
        fill="#727272"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.4998 12H21.7498V21.75H16.4998V12ZM20.2498 20.25V13.5H17.9998V20.25H20.2498Z"
        fill="#727272"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9998 15H9.74983V21.75H14.9998V15ZM13.4998 16.5V20.25H11.2498V16.5H13.4998Z"
        fill="#727272"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.99983 10.5H8.24983V21.75H2.99983V10.5ZM6.74983 20.25V12H4.49983V20.25H6.74983Z"
        fill="#727272"
      />
    </svg>
  );
}

export function CogIcon({ className, size = 24 }: IconProps) {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.0001 8.25C9.92907 8.25 8.25014 9.92893 8.25014 12C8.25014 14.0711 9.92907 15.75 12.0001 15.75C14.0712 15.75 15.7501 14.0711 15.7501 12C15.7501 9.92893 14.0712 8.25 12.0001 8.25ZM9.75014 12C9.75014 10.7574 10.7575 9.75 12.0001 9.75C13.2428 9.75 14.2501 10.7574 14.2501 12C14.2501 13.2426 13.2428 14.25 12.0001 14.25C10.7575 14.25 9.75014 13.2426 9.75014 12Z"
        fill="#727272"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.115 2.25H9.88528L9.33799 4.98641C8.57784 5.27508 7.87722 5.68352 7.25908 6.18833L4.61388 5.29349L2.49902 8.95652L4.59584 10.7993C4.53282 11.1906 4.50013 11.5917 4.50013 12C4.50013 12.4083 4.53282 12.8094 4.59584 13.2007L2.49902 15.0435L4.61388 18.7065L7.25909 17.8117C7.87723 18.3165 8.57784 18.7249 9.33799 19.0136L9.88528 21.75H14.115L14.6623 19.0136C15.4224 18.7249 16.1231 18.3165 16.7412 17.8116L19.3865 18.7065L21.5013 15.0435L19.4044 13.2007C19.4674 12.8093 19.5001 12.4083 19.5001 12C19.5001 11.5918 19.4674 11.1907 19.4044 10.7994L21.5014 8.95651L19.3866 5.29348L16.7412 6.18836C16.1231 5.68354 15.4224 5.27509 14.6623 4.98641L14.115 2.25ZM10.6373 6.13819L11.115 3.75H12.8853L13.3629 6.13819L13.7879 6.27064C14.6452 6.53782 15.4207 6.99262 16.0662 7.58784L16.3934 7.88953L18.7024 7.10843L19.5875 8.64156L17.7582 10.2492L17.8553 10.683C17.95 11.1061 18.0001 11.5468 18.0001 12C18.0001 12.4533 17.95 12.8939 17.8553 13.3171L17.7582 13.7508L19.5874 15.3584L18.7023 16.8916L16.3934 16.1105L16.0662 16.4122C15.4207 17.0074 14.6452 17.4622 13.7879 17.7294L13.3629 17.8618L12.8853 20.25H11.115L10.6373 17.8618L10.2124 17.7294C9.35512 17.4622 8.57956 17.0074 7.93406 16.4122L7.60688 16.1105L5.29806 16.8916L4.41291 15.3584L6.24209 13.7509L6.14499 13.3171C6.05026 12.894 6.00013 12.4533 6.00013 12C6.00013 11.5467 6.05026 11.106 6.14499 10.6829L6.24209 10.2491L4.41291 8.64157L5.29806 7.10845L7.60688 7.88949L7.93405 7.58781C8.57955 6.99261 9.35511 6.53782 10.2124 6.27064L10.6373 6.13819Z"
        fill="#727272"
      />
    </svg>
  );
}

export function CogFilledIcon({ className, size = 24 }: IconProps) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 27 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.4688 2.32995L9.49611 4.8915L6.14821 6.82995L3.47895 6.41456C3.0345 6.35301 2.58213 6.42767 2.17934 6.62901C1.77657 6.83036 1.44153 7.1493 1.21686 7.54534L0.312027 9.16071C0.0801666 9.56306 -0.0266591 10.0277 0.00564889 10.4932C0.0379569 10.9588 0.207882 11.4034 0.492995 11.7684L2.18957 13.9146V17.7915L0.538236 19.9376C0.253123 20.3026 0.0831998 20.7473 0.0508919 21.2128C0.0185818 21.6784 0.125408 22.143 0.357268 22.5454L1.26211 24.1607C1.48678 24.5567 1.82181 24.8757 2.22458 25.077C2.62736 25.2784 3.07972 25.353 3.52419 25.2915L6.19346 24.8762L9.49611 26.8146L10.4688 29.3762C10.6328 29.8099 10.9215 30.183 11.2968 30.4463C11.6722 30.7099 12.1169 30.8517 12.5725 30.853H14.4727C14.9284 30.8517 15.373 30.7099 15.7484 30.4463C16.1238 30.183 16.4124 29.8099 16.5765 29.3762L17.5491 26.8146L20.8518 24.8762L23.5211 25.2915C23.9656 25.353 24.4179 25.2784 24.8207 25.077C25.2234 24.8757 25.5584 24.5567 25.7832 24.1607L26.6881 22.5454C26.9198 22.143 27.0267 21.6784 26.9943 21.2128C26.962 20.7473 26.7921 20.3026 26.507 19.9376L24.8104 17.7915V13.9146L26.4619 11.7684C26.7469 11.4034 26.9168 10.9588 26.9492 10.4932C26.9815 10.0277 26.8746 9.56306 26.6427 9.16071L25.7378 7.54534C25.5133 7.1493 25.1782 6.83036 24.7754 6.62901C24.3727 6.42767 23.9202 6.35301 23.4758 6.41456L20.8067 6.82995L17.5039 4.8915L16.5312 2.32995C16.3672 1.89625 16.0785 1.52321 15.7032 1.25967C15.3278 0.996136 14.8831 0.854401 14.4275 0.853027H12.5725C12.1169 0.854401 11.6722 0.996136 11.2968 1.25967C10.9215 1.52321 10.6328 1.89625 10.4688 2.32995ZM13.5 20.6745C16.1102 20.6745 18.2262 18.5158 18.2262 15.853C18.2262 13.1902 16.1102 11.0316 13.5 11.0316C10.8898 11.0316 8.77387 13.1902 8.77387 15.853C8.77387 18.5158 10.8898 20.6745 13.5 20.6745Z"
        fill="black"
      />
    </svg>
  );
}

export function SignalIcon({ className, size = 24 }: IconProps) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 33 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_424_1123)">
        <path
          d="M13.5096 20.0107C12.5692 19.3544 11.7935 18.4894 11.2434 17.4832C10.6933 16.4771 10.3837 15.3572 10.3389 14.2113C10.3762 13.0571 10.6822 11.9274 11.2326 10.9121C11.783 9.89684 12.5627 9.02394 13.5096 8.36279"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.257 22.3761C8.92585 21.4502 7.8309 20.2243 7.06041 18.7975C6.28994 17.3707 5.86549 15.7828 5.82129 14.1619C5.88007 12.5595 6.30976 10.9926 7.0766 9.58433C7.84342 8.17611 8.92647 6.96496 10.2406 6.04614"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M19.4902 20.0107C20.435 19.3511 21.2133 18.4808 21.7636 17.4686C22.314 16.4564 22.6212 15.33 22.6609 14.1785C22.6185 13.0299 22.3102 11.9067 21.76 10.8975C21.2098 9.88832 20.4328 9.02064 19.4902 8.36279"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M22.7422 22.3759C24.0734 21.4498 25.1682 20.224 25.9387 18.7972C26.7092 17.3704 27.1337 15.7825 27.1779 14.1616C27.1342 12.5423 26.7118 10.9558 25.9443 9.52929C25.1769 8.10278 24.0857 6.87597 22.7586 5.94727"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.5004 16.7247C17.8614 16.7247 18.9647 15.6214 18.9647 14.2604C18.9647 12.8994 17.8614 11.7961 16.5004 11.7961C15.1394 11.7961 14.0361 12.8994 14.0361 14.2604C14.0361 15.6214 15.1394 16.7247 16.5004 16.7247Z"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.5 16.7246V27.1718"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_424_1123">
          <rect
            width="23"
            height="23"
            fill="white"
            transform="translate(5 5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ESimIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.6287 3.75H6.75V20.25H17.25V7.37132L13.6287 3.75ZM14.25 2.25H5.25V21.75H18.75V6.75L14.25 2.25Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.25 11.25V18.75H15.75V11.25H8.25ZM14.25 12.75H12.75V14.25H14.25V12.75ZM14.25 15.75H12.75V17.25H14.25V15.75ZM9.75 12.75H11.25V17.25H9.75V12.75Z"
        fill="#141414"
      />
    </svg>
  );
}

export function USwitchDashOverviewHouse({
  className
}: {
  className?: string;
}) {
  return (
    <svg
      className={className}
      width="172"
      height="122"
      viewBox="0 0 135 96"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M0.650391 54.5074L73.6307 55.263L101.713 12.835H25.6989L0.650391 54.5074Z"
        fill="#141424"
      />
      <path
        d="M123.355 95.9999H11.8604V54.1245L29.7473 24.8467H84.7504V52.6132H123.355V95.9999Z"
        fill="#D9F4FA"
      />
      <path
        d="M123.356 39.709H55.6875V95.9977H123.356V39.709Z"
        fill="#9CC4E5"
      />
      <path
        d="M134.35 56.7855H51.1293L23.0244 17.2785L25.6973 12.835H106.662L134.35 56.7855Z"
        fill="#141424"
      />
      <path
        d="M91.877 70.1499C96.4108 70.1499 100.099 73.8378 100.099 78.3716V95.8639H83.6553V78.3716C83.6553 73.8378 87.3432 70.1499 91.877 70.1499Z"
        fill="#141424"
      />
      <path
        d="M39.3233 63.9463H25.1807V80.9536H39.3233V63.9463Z"
        fill="#2C6CF1"
      />
      <path
        d="M96.4455 44.5938H79.6186L71.6338 33H88.0997L96.4455 44.5938Z"
        fill="#B6D6DD"
      />
      <path
        d="M101.723 32.9998L90.7947 23.436L79.6182 32.9998V44.5937H101.723V32.9998Z"
        fill="#D9F4FA"
      />
      <path
        d="M95.9488 34.0488H86.5654V44.5938H95.9488V34.0488Z"
        fill="#B6D6DD"
      />
      <path d="M51.9437 0H35.5342V16.4096H51.9437V0Z" fill="#141424" />
    </svg>
  );
}

export function UserProfileIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 13.5C14.0711 13.5 15.75 11.8211 15.75 9.75C15.75 7.67893 14.0711 6 12 6C9.92893 6 8.25 7.67893 8.25 9.75C8.25 11.8211 9.92893 13.5 12 13.5ZM12 12C13.2426 12 14.25 10.9926 14.25 9.75C14.25 8.50736 13.2426 7.5 12 7.5C10.7574 7.5 9.75 8.50736 9.75 9.75C9.75 10.9926 10.7574 12 12 12Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.5 12C1.5 6.20101 6.20101 1.5 12 1.5C17.799 1.5 22.5 6.20101 22.5 12C22.5 17.799 17.799 22.5 12 22.5C6.20101 22.5 1.5 17.799 1.5 12ZM12 3C7.02944 3 3 7.02944 3 12C3 14.6656 4.15885 17.0606 6.00023 18.7085C6.02246 16.6566 7.69278 15 9.75 15H14.25C16.3072 15 17.9775 16.6566 17.9998 18.7085C19.8412 17.0606 21 14.6656 21 12C21 7.02944 16.9706 3 12 3ZM16.5 19.796V18.75C16.5 17.5074 15.4926 16.5 14.25 16.5H9.75C8.50736 16.5 7.5 17.5074 7.5 18.75V19.796C8.82378 20.5617 10.3607 21 12 21C13.6393 21 15.1762 20.5617 16.5 19.796Z"
        fill="#141414"
      />
    </svg>
  );
}

export function SearchIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.5455 2.54541C6.3281 2.54541 2.90918 5.96433 2.90918 10.1818C2.90918 14.3992 6.3281 17.8181 10.5455 17.8181C12.3924 17.8181 14.0861 17.1625 15.4066 16.0713L20.5768 21.2415L21.6053 20.213L16.4351 15.0428C17.5263 13.7223 18.1819 12.0286 18.1819 10.1818C18.1819 5.96433 14.763 2.54541 10.5455 2.54541ZM4.36373 10.1818C4.36373 6.76765 7.13142 3.99996 10.5455 3.99996C13.9597 3.99996 16.7274 6.76765 16.7274 10.1818C16.7274 13.5959 13.9597 16.3636 10.5455 16.3636C7.13142 16.3636 4.36373 13.5959 4.36373 10.1818Z"
        fill="#141414"
      />
    </svg>
  );
}

export function MobileMenuIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path d="M20.25 7.5L3.75 7.5V6L20.25 6V7.5Z" fill="#141414" />
      <path
        d="M20.25 12.75L3.75 12.75V11.25L20.25 11.25V12.75Z"
        fill="#141414"
      />
      <path d="M3.75 18L20.25 18V16.5L3.75 16.5V18Z" fill="#141414" />
    </svg>
  );
}

export function BellIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M11.25 11.875C11.25 12.5654 10.6904 13.125 10 13.125C9.30964 13.125 8.75 12.5654 8.75 11.875C8.75 11.1846 9.30964 10.625 10 10.625C10.6904 10.625 11.25 11.1846 11.25 11.875Z"
        fill="#141414"
      />
      <path d="M9.375 5.625V10.625H10.625V5.625H9.375Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 1.875C6.71582 1.875 4.08654 4.61627 4.08654 7.95635V11.7959C3.65703 12.0573 3.27183 12.3711 2.99048 12.8247C2.63954 13.3906 2.5 14.0917 2.5 15V15.625H17.5V15C17.5 14.1902 17.3892 13.5444 17.1153 13.0118C16.8353 12.4671 16.4195 12.108 15.9411 11.8129C15.9273 11.8044 15.9192 11.7948 15.9156 11.7885L15.9142 11.7861L15.9135 11.7843L15.9135 7.95635C15.9135 4.61627 13.2842 1.875 10 1.875ZM5.33654 7.95635C5.33654 5.26951 7.44271 3.125 10 3.125C12.5573 3.125 14.6635 5.26951 14.6635 7.95635L14.6635 11.7854C14.6635 12.2547 14.9244 12.6545 15.2849 12.8768C15.6478 13.1007 15.863 13.3099 16.0037 13.5834C16.101 13.7727 16.1775 14.022 16.2181 14.375H3.78193C3.82992 13.9573 3.92839 13.6841 4.05279 13.4835C4.23 13.1978 4.50944 12.9771 5.00731 12.7097L5.33654 12.5328V7.95635Z"
        fill="#141414"
      />
      <path d="M7.5 16.875V18.125H12.5V16.875H7.5Z" fill="#141414" />
    </svg>
  );
}

export function PlusButtonCircled({
  className,
  fill = '#141414'
}: {
  className?: string;
  fill?: string;
}) {
  return (
    <svg
      className={className}
      width="25"
      height="24"
      viewBox="0 0 25 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M13.25 11.25H17V12.75H13.25V16.5H11.75V12.75H8V11.25H11.75V7.5H13.25V11.25Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2 12C2 6.20101 6.70101 1.5 12.5 1.5C18.299 1.5 23 6.20101 23 12C23 17.799 18.299 22.5 12.5 22.5C6.70101 22.5 2 17.799 2 12ZM12.5 3C7.52944 3 3.5 7.02944 3.5 12C3.5 16.9706 7.52944 21 12.5 21C17.4706 21 21.5 16.9706 21.5 12C21.5 7.02944 17.4706 3 12.5 3Z"
        fill={fill}
      />
    </svg>
  );
}

export function GoBackIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M5.31 11.2498L10.3352 4.96829L9.16388 4.03125L2.78906 11.9998L9.16388 19.9683L10.3352 19.0313L5.31 12.7498H20.9995V11.2498H5.31Z"
        fill="#141424"
      />
    </svg>
  );
}

export function WarningIcon({ className, size = 20 }: IconProps) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        d="M10 1.25C14.8325 1.25 18.75 5.16751 18.75 10C18.75 14.8325 14.8325 18.75 10 18.75C5.16751 18.75 1.25 14.8325 1.25 10C1.25 5.16751 5.16751 1.25 10 1.25ZM10 11.875C9.30964 11.875 8.75 12.4346 8.75 13.125C8.75 13.8154 9.30964 14.375 10 14.375C10.6904 14.375 11.25 13.8154 11.25 13.125C11.25 12.4346 10.6904 11.875 10 11.875ZM9.375 5.625V10.625H10.625V5.625H9.375Z"
        fill="#EA0040"
      />
    </svg>
  );
}

export function EnvelopeIcon({ className, size = 24 }: IconProps) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.5 4.5H1.5V19.5H22.5V4.5ZM3 6.77314V16.6489L7.95909 10.5878L3 6.77314ZM20.1673 18L14.8514 11.5028L12 13.6962L9.14857 11.5028L3.83268 18H20.1673ZM16.0409 10.5878L21 6.77314V16.6489L16.0409 10.5878ZM4.4551 6L12 11.8038L19.5449 6H4.4551Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ArrowDown({ className }: IconProps) {
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.37561 19.781L16.0003 28.2808L26.625 19.781L25.3756 18.2193L17.0003 24.9195L17.0003 4.00012H15.0003L15.0003 24.9195L6.625 18.2193L5.37561 19.781Z"
        fill="#141414"
      />
    </svg>
  );
}

export function EditIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.0607 8.25011L15.75 1.93945L2.25 15.4395V21.7501H8.56066L22.0607 8.25011ZM3.75 20.2501V16.0608L13.125 6.68579L17.3143 10.8751L7.93934 20.2501H3.75ZM18.375 9.81446L19.9393 8.25011L15.75 4.06077L14.1856 5.62513L18.375 9.81446Z"
        fill="#141414"
      />
    </svg>
  );
}

export function DeliverooTickIcon() {
  return (
    <svg
      width="21"
      height="16"
      viewBox="0 0 21 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.3128 2.78419L17.5286 0L7.80357 9.72506L2.78419 4.74489L0 7.48986L7.80357 15.2934L20.3128 2.78419Z"
        fill="#00CCBB"
      />
    </svg>
  );
}

export function DeliverooIncrementIcon() {
  return (
    <svg
      width="12"
      height="5"
      className="h-4 w-6"
      viewBox="0 0 11 4"
      fill="#C8C8C8"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="1.33776"
        y="0.774772"
        width="8.89167"
        height="2.425"
        fill="#43CCBC"
        stroke="#43CCBC"
        strokeWidth="0.808333"
      />
    </svg>
  );
}
export function DeliverooDecrementIcon() {
  return (
    <svg
      width="11"
      height="10"
      viewBox="0 0 11 10"
      className="h-4 w-6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.8335 3.37073H10.0669V6.60413H6.8335V9.83752H3.6001V6.60413H0.366699V3.37073H3.6001V0.137329H6.8335V3.37073Z"
        fill="#43CCBC"
      />
    </svg>
  );
}

export function ShakaLogoWhite({ className = '' }: { className?: string }) {
  return (
    <svg
      className={className}
      width="132"
      height="24"
      viewBox="0 0 469 84"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M44.5885 40.6927C44.5885 33.9386 50.0638 28.4633 56.818 28.4633C63.5721 28.4633 69.0474 33.9386 69.0474 40.6927V71.2663C69.0474 78.0204 63.5721 83.4957 56.818 83.4957C50.0638 83.4957 44.5885 78.0204 44.5885 71.2663V40.6927Z"
        fill="url(#paint0_linear_4009_7531)"
      />
      <path
        d="M44.5885 40.6927C44.5885 33.9386 50.0638 28.4633 56.818 28.4633C63.5721 28.4633 69.0474 33.9386 69.0474 40.6927V71.2663C69.0474 78.0204 63.5721 83.4957 56.818 83.4957C50.0638 83.4957 44.5885 78.0204 44.5885 71.2663V40.6927Z"
        fill="url(#paint1_linear_4009_7531)"
      />
      <path
        d="M75.1621 40.6927C75.1621 33.9386 80.6374 28.4633 87.3915 28.4633C94.1456 28.4633 99.6209 33.9386 99.6209 40.6927V71.2663C99.6209 78.0204 94.1456 83.4957 87.3915 83.4957C80.6374 83.4957 75.1621 78.0204 75.1621 71.2663V40.6927Z"
        fill="url(#paint2_linear_4009_7531)"
      />
      <path
        d="M75.1621 40.6927C75.1621 33.9386 80.6374 28.4633 87.3915 28.4633C94.1456 28.4633 99.6209 33.9386 99.6209 40.6927V71.2663C99.6209 78.0204 94.1456 83.4957 87.3915 83.4957C80.6374 83.4957 75.1621 78.0204 75.1621 71.2663V40.6927Z"
        fill="url(#paint3_linear_4009_7531)"
      />
      <path
        d="M116.675 9.86385C117.983 3.23755 124.415 -1.074 131.041 0.233753C137.667 1.5415 141.979 7.97333 140.671 14.5996L129.043 73.5193C127.735 80.1456 121.303 84.4572 114.677 83.1494C108.051 81.8417 103.739 75.4099 105.047 68.7835L116.675 9.86385Z"
        fill="url(#paint4_linear_4009_7531)"
      />
      <path
        d="M116.675 9.86385C117.983 3.23755 124.415 -1.074 131.041 0.233753C137.667 1.5415 141.979 7.97333 140.671 14.5996L129.043 73.5193C127.735 80.1456 121.303 84.4572 114.677 83.1494C108.051 81.8417 103.739 75.4099 105.047 68.7835L116.675 9.86385Z"
        fill="url(#paint5_linear_4009_7531)"
      />
      <path
        d="M0.767169 35.3137C-1.58244 28.9815 1.64614 21.9434 7.9784 19.5938C14.3107 17.2442 21.3487 20.4728 23.6983 26.8051L38.2179 65.9358C40.5675 72.2681 37.3389 79.3061 31.0067 81.6557C24.6744 84.0053 17.6364 80.7767 15.2867 74.4445L0.767169 35.3137Z"
        fill="url(#paint6_linear_4009_7531)"
      />
      <path
        d="M0.767169 35.3137C-1.58244 28.9815 1.64614 21.9434 7.9784 19.5938C14.3107 17.2442 21.3487 20.4728 23.6983 26.8051L38.2179 65.9358C40.5675 72.2681 37.3389 79.3061 31.0067 81.6557C24.6744 84.0053 17.6364 80.7767 15.2867 74.4445L0.767169 35.3137Z"
        fill="url(#paint7_linear_4009_7531)"
      />
      <path
        d="M177.593 64.6476H191.65C192.174 69.3681 196.265 72.62 203.503 72.62C209.692 72.62 213.469 70.6269 213.469 66.8505C213.469 62.4447 209.797 62.13 201.091 60.9761C188.503 59.5075 178.747 56.6752 178.747 45.5559C178.747 34.8561 188.083 27.8278 201.93 27.9327C216.406 27.9327 226.371 34.3316 227.106 45.3461H212.944C212.525 41.0452 208.434 38.4227 202.454 38.4227C196.685 38.4227 192.908 40.5207 192.908 43.9824C192.908 48.0735 197.524 48.4931 205.287 49.4372C217.665 50.696 227.945 53.5283 227.945 65.6966C227.945 76.3964 217.875 83.2149 203.398 83.2149C188.398 83.2149 178.222 76.1866 177.593 64.6476Z"
        fill="white"
      />
      <path
        d="M262.591 28.0376C274.549 28.0376 285.771 34.7115 288.291 52.6891L293.203 83.2141L278.927 83.3356L274.009 53.9332C272.37 45.0039 270.143 40.8207 262.591 40.8207C255.038 40.8207 249.424 45.451 251.063 55.5119L255.98 83.2149H241.516L227.945 9.15564H241.516L246.145 35.8432C249.424 30.4929 254.933 28.0376 262.591 28.0376Z"
        fill="white"
      />
      <path
        d="M349.012 70.1024H350.69V82.5855H343.137C335.479 82.5855 333.067 78.914 333.172 73.7739C329.395 79.7532 323.941 83.2149 315.549 83.2149C303.905 83.2149 295.513 77.6552 295.513 67.375C295.513 55.941 304.114 49.4372 320.269 49.4372H330.969V46.8147C330.969 41.9893 327.507 38.8423 321.318 38.8423C315.549 38.8423 311.667 41.4648 311.038 45.451H296.771C297.82 34.961 307.471 28.0376 321.738 28.0376C336.843 28.0376 345.55 34.7512 345.55 47.6539V66.7456C345.55 69.5779 346.704 70.1024 349.012 70.1024ZM330.969 60.3467V59.2977H320.164C313.975 59.2977 310.408 61.8153 310.408 66.326C310.408 70.1024 313.451 72.5151 318.381 72.5151C326.143 72.5151 330.864 67.6897 330.969 60.3467Z"
        fill="white"
      />
      <path
        d="M351.691 82.5855L364.594 9.15564H379.385L371.832 51.2205L396.484 28.667H414.526L390.504 50.696L404.98 82.5855H389.141L379.28 60.4516L368.79 69.8926L366.482 82.5855H351.691Z"
        fill="white"
      />
      <path
        d="M466.409 70.1024H468.088V82.5855H460.535C452.877 82.5855 450.464 78.914 450.569 73.7739C446.793 79.7532 441.338 83.2149 432.946 83.2149C421.302 83.2149 412.91 77.6552 412.91 67.375C412.91 55.941 421.512 49.4372 437.667 49.4372H448.366V46.8147C448.366 41.9893 444.905 38.8423 438.716 38.8423C432.946 38.8423 429.065 41.4648 428.435 45.451H414.169C415.218 34.961 424.869 28.0376 439.135 28.0376C454.241 28.0376 462.947 34.7512 462.947 47.6539V66.7456C462.947 69.5779 464.101 70.1024 466.409 70.1024ZM448.366 60.3467V59.2977H437.562C431.373 59.2977 427.806 61.8153 427.806 66.326C427.806 70.1024 430.848 72.5151 435.778 72.5151C443.541 72.5151 448.262 67.6897 448.366 60.3467Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export function ShakaLogoBlack({ className = '' }: { className?: string }) {
  return (
    <svg
      className={className}
      width="132"
      height="24"
      viewBox="0 0 469 84"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M44.5885 40.6927C44.5885 33.9386 50.0638 28.4633 56.818 28.4633C63.5721 28.4633 69.0474 33.9386 69.0474 40.6927V71.2663C69.0474 78.0204 63.5721 83.4957 56.818 83.4957C50.0638 83.4957 44.5885 78.0204 44.5885 71.2663V40.6927Z"
        fill="url(#paint0_linear_4009_7531)"
      />
      <path
        d="M44.5885 40.6927C44.5885 33.9386 50.0638 28.4633 56.818 28.4633C63.5721 28.4633 69.0474 33.9386 69.0474 40.6927V71.2663C69.0474 78.0204 63.5721 83.4957 56.818 83.4957C50.0638 83.4957 44.5885 78.0204 44.5885 71.2663V40.6927Z"
        fill="url(#paint1_linear_4009_7531)"
      />
      <path
        d="M75.1621 40.6927C75.1621 33.9386 80.6374 28.4633 87.3915 28.4633C94.1456 28.4633 99.6209 33.9386 99.6209 40.6927V71.2663C99.6209 78.0204 94.1456 83.4957 87.3915 83.4957C80.6374 83.4957 75.1621 78.0204 75.1621 71.2663V40.6927Z"
        fill="url(#paint2_linear_4009_7531)"
      />
      <path
        d="M75.1621 40.6927C75.1621 33.9386 80.6374 28.4633 87.3915 28.4633C94.1456 28.4633 99.6209 33.9386 99.6209 40.6927V71.2663C99.6209 78.0204 94.1456 83.4957 87.3915 83.4957C80.6374 83.4957 75.1621 78.0204 75.1621 71.2663V40.6927Z"
        fill="url(#paint3_linear_4009_7531)"
      />
      <path
        d="M116.675 9.86385C117.983 3.23755 124.415 -1.074 131.041 0.233753C137.667 1.5415 141.979 7.97333 140.671 14.5996L129.043 73.5193C127.735 80.1456 121.303 84.4572 114.677 83.1494C108.051 81.8417 103.739 75.4099 105.047 68.7835L116.675 9.86385Z"
        fill="url(#paint4_linear_4009_7531)"
      />
      <path
        d="M116.675 9.86385C117.983 3.23755 124.415 -1.074 131.041 0.233753C137.667 1.5415 141.979 7.97333 140.671 14.5996L129.043 73.5193C127.735 80.1456 121.303 84.4572 114.677 83.1494C108.051 81.8417 103.739 75.4099 105.047 68.7835L116.675 9.86385Z"
        fill="url(#paint5_linear_4009_7531)"
      />
      <path
        d="M0.767169 35.3137C-1.58244 28.9815 1.64614 21.9434 7.9784 19.5938C14.3107 17.2442 21.3487 20.4728 23.6983 26.8051L38.2179 65.9358C40.5675 72.2681 37.3389 79.3061 31.0067 81.6557C24.6744 84.0053 17.6364 80.7767 15.2867 74.4445L0.767169 35.3137Z"
        fill="url(#paint6_linear_4009_7531)"
      />
      <path
        d="M0.767169 35.3137C-1.58244 28.9815 1.64614 21.9434 7.9784 19.5938C14.3107 17.2442 21.3487 20.4728 23.6983 26.8051L38.2179 65.9358C40.5675 72.2681 37.3389 79.3061 31.0067 81.6557C24.6744 84.0053 17.6364 80.7767 15.2867 74.4445L0.767169 35.3137Z"
        fill="url(#paint7_linear_4009_7531)"
      />
      <path
        d="M177.593 64.6476H191.65C192.174 69.3681 196.265 72.62 203.503 72.62C209.692 72.62 213.469 70.6269 213.469 66.8505C213.469 62.4447 209.797 62.13 201.091 60.9761C188.503 59.5075 178.747 56.6752 178.747 45.5559C178.747 34.8561 188.083 27.8278 201.93 27.9327C216.406 27.9327 226.371 34.3316 227.106 45.3461H212.944C212.525 41.0452 208.434 38.4227 202.454 38.4227C196.685 38.4227 192.908 40.5207 192.908 43.9824C192.908 48.0735 197.524 48.4931 205.287 49.4372C217.665 50.696 227.945 53.5283 227.945 65.6966C227.945 76.3964 217.875 83.2149 203.398 83.2149C188.398 83.2149 178.222 76.1866 177.593 64.6476Z"
        fill="black"
      />
      <path
        d="M262.591 28.0376C274.549 28.0376 285.771 34.7115 288.291 52.6891L293.203 83.2141L278.927 83.3356L274.009 53.9332C272.37 45.0039 270.143 40.8207 262.591 40.8207C255.038 40.8207 249.424 45.451 251.063 55.5119L255.98 83.2149H241.516L227.945 9.15564H241.516L246.145 35.8432C249.424 30.4929 254.933 28.0376 262.591 28.0376Z"
        fill="black"
      />
      <path
        d="M349.012 70.1024H350.69V82.5855H343.137C335.479 82.5855 333.067 78.914 333.172 73.7739C329.395 79.7532 323.941 83.2149 315.549 83.2149C303.905 83.2149 295.513 77.6552 295.513 67.375C295.513 55.941 304.114 49.4372 320.269 49.4372H330.969V46.8147C330.969 41.9893 327.507 38.8423 321.318 38.8423C315.549 38.8423 311.667 41.4648 311.038 45.451H296.771C297.82 34.961 307.471 28.0376 321.738 28.0376C336.843 28.0376 345.55 34.7512 345.55 47.6539V66.7456C345.55 69.5779 346.704 70.1024 349.012 70.1024ZM330.969 60.3467V59.2977H320.164C313.975 59.2977 310.408 61.8153 310.408 66.326C310.408 70.1024 313.451 72.5151 318.381 72.5151C326.143 72.5151 330.864 67.6897 330.969 60.3467Z"
        fill="black"
      />
      <path
        d="M351.691 82.5855L364.594 9.15564H379.385L371.832 51.2205L396.484 28.667H414.526L390.504 50.696L404.98 82.5855H389.141L379.28 60.4516L368.79 69.8926L366.482 82.5855H351.691Z"
        fill="black"
      />
      <path
        d="M466.409 70.1024H468.088V82.5855H460.535C452.877 82.5855 450.464 78.914 450.569 73.7739C446.793 79.7532 441.338 83.2149 432.946 83.2149C421.302 83.2149 412.91 77.6552 412.91 67.375C412.91 55.941 421.512 49.4372 437.667 49.4372H448.366V46.8147C448.366 41.9893 444.905 38.8423 438.716 38.8423C432.946 38.8423 429.065 41.4648 428.435 45.451H414.169C415.218 34.961 424.869 28.0376 439.135 28.0376C454.241 28.0376 462.947 34.7512 462.947 47.6539V66.7456C462.947 69.5779 464.101 70.1024 466.409 70.1024ZM448.366 60.3467V59.2977H437.562C431.373 59.2977 427.806 61.8153 427.806 66.326C427.806 70.1024 430.848 72.5151 435.778 72.5151C443.541 72.5151 448.262 67.6897 448.366 60.3467Z"
        fill="black"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="105.5"
          y2="42"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E08EA3" />
          <stop offset="1" stopColor="#987DCE" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_4009_7531"
          x1="7.97828"
          y1="19.5939"
          x2="141"
          y2="52.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.265155" stopColor="#F19DE8" />
          <stop offset="0.760189" stopColor="#987DCE" />
        </linearGradient>
      </defs>
    </svg>
  );
}
