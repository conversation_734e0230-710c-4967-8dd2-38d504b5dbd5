import { StripeMode } from '@/lib/stripe/types';
import { API_ENDPOINTS, API_HOST } from '@/auth/api/endpoints';
import { AxiosClient } from '@/lib/axios-client';
import { getClientConfig } from '@/client-config/client-config';

export interface SessionResponse {
  clientSecret: string;
  sessionId: string;
  totalToPay: number;
}

export interface SessionStatusResponse {
  customer_email: null;
  expires_at: number;
  id: string;
  payment_status: string;
  status: string;
  subscription: string;
}

export interface PurchaseIntentOrderItem {
  id: number;
  type: string;
  name: string;
  price: string;
  quantity: number;
}

export interface PurchaseIntentOrderSummary {
  items: PurchaseIntentOrderItem[];
  total_cost: string;
  currency: string;
}

export interface PurchaseIntentStripeLineItem {
  price: string;
  quantity: number;
}

export interface PurchaseIntentBasketEntry {
  id: string;
  correlation_id: string;
}

export interface PurchaseDetails {
  basket: PurchaseIntentBasketEntry[];
  ui_mode: string;
  return_path: string;
  total_plans: number;
  total_addons: number;
  correlation_id: string;
  total_child_plans: number;
}

export interface PurchaseIntentDetails {
  purchase_intent_id: string;
  purchase_type: string;
  status: string;
  created_at: string;
  stripe_session_id: string;
  original_basket: Record<string, any>;
  stripe_line_items: PurchaseIntentStripeLineItem[];
  order_summary: PurchaseIntentOrderSummary;
  purchase_details: PurchaseDetails;
}

export const paymentService = {
  createCheckoutSession: async (
    apiClient: AxiosClient,
    clientId: string,
    paymentMode: StripeMode,
    returnUrl: string,
    payload?: any
  ): Promise<SessionResponse> => {
    const response = await apiClient.post(
      `${API_HOST}/s/api/v1/${clientId}/${API_ENDPOINTS.stripe.createCheckoutSession(clientId, paymentMode, returnUrl)}`,
      payload
    );

    return {
      clientSecret: response.client_secret,
      sessionId: response.session_id,
      totalToPay: response.amount_total
    };
  },
  createSetupSession: async (
    apiClient: AxiosClient,
    returnUrl: string
  ): Promise<{ clientSecret: string; sessionId: string }> => {
    const clientId = getClientConfig().clientId;
    const response = await apiClient.post(
      `${API_HOST}/s/api/v1/${clientId}/${API_ENDPOINTS.stripe.createSetupSession(clientId, returnUrl)}`
    );

    return {
      clientSecret: response.client_secret,
      sessionId: response.session_id
    };
  },
  checkoutSessionStatus: async (
    apiClient: AxiosClient,
    sessionId: string
  ): Promise<SessionStatusResponse> => {
    const clientId = getClientConfig().clientId;
    return await apiClient.get(
      `${API_HOST}/s/api/v1/${clientId}/${API_ENDPOINTS.stripe.checkoutSessionStatus(sessionId)}`
    );
  },
  getPurchaseIntentDetails: async (
    apiClient: AxiosClient,
    sessionId: string
  ): Promise<PurchaseIntentDetails> => {
    const clientId = getClientConfig().clientId;
    return await apiClient.get(
      `${API_HOST}/s/api/v1/${clientId}/${API_ENDPOINTS.stripe.purchaseIntentDetails(sessionId)}`
    );
  }
};
